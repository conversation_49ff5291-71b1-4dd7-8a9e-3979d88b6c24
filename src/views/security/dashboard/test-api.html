<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BI接口测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .api-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .api-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .loading {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BI大屏展示接口测试</h1>
        
        <div class="api-section">
            <h2 class="api-title">01 业务异常情况统计</h2>
            <p>接口: GET /bi/onwer/selectEventExceptionStats</p>
            <button class="test-button" onclick="testEventExceptionStats()">测试接口</button>
            <div id="result1" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <h2 class="api-title">02 工程车辆检修情况统计</h2>
            <p>接口: GET /bi/onwer/selectVehicleDailyEventStats</p>
            <button class="test-button" onclick="testVehicleDailyEventStats()">测试接口</button>
            <div id="result2" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <h2 class="api-title">03 作业管控情况统计</h2>
            <p>接口: GET /bi/onwer/selectOperationDailyEventStats</p>
            <button class="test-button" onclick="testOperationDailyEventStats()">测试接口</button>
            <div id="result3" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <h2 class="api-title">04 模型总调用趋势</h2>
            <p>接口: GET /bi/onwer/selectModelTotalCallTrend</p>
            <button class="test-button" onclick="testModelTotalCallTrend()">测试接口</button>
            <div id="result4" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <h2 class="api-title">05 今日指标统计</h2>
            <p>接口: GET /bi/onwer/selectDailyStats</p>
            <button class="test-button" onclick="testDailyStats()">测试接口</button>
            <div id="result5" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <h2 class="api-title">06 AR眼镜实时画面统计</h2>
            <p>接口: GET /bi/onwer/selectArGlassRealTimeScreenStats</p>
            <button class="test-button" onclick="testArGlassRealTimeScreenStats()">测试接口</button>
            <div id="result6" class="result" style="display: none;"></div>
        </div>

        <div class="api-section">
            <h2 class="api-title">批量测试</h2>
            <button class="test-button" onclick="testAllApis()">测试所有接口</button>
        </div>
    </div>

    <script>
        // 获取当前月份范围
        function getMonthRange(months = 1) {
            const now = new Date();
            const endMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
            
            const startDate = new Date(now);
            startDate.setMonth(startDate.getMonth() - months + 1);
            const startMonth = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;
            
            return { startMonth, endMonth };
        }

        // 通用API调用函数
        async function callApi(url, params = {}, resultId) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在调用接口...';

            try {
                const queryString = new URLSearchParams(params).toString();
                const fullUrl = queryString ? `${url}?${queryString}` : url;
                
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 这里需要根据实际情况添加认证头
                        // 'Authorization': 'Bearer your-token',
                        // 'glasses-Authorization': 'your-security-token'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 接口调用成功\n\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 接口调用失败\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 接口调用异常\n\n错误信息: ${error.message}`;
            }
        }

        // 各个接口测试函数
        function testEventExceptionStats() {
            const { startMonth, endMonth } = getMonthRange(1);
            callApi('/bi/onwer/selectEventExceptionStats', { startMonth, endMonth }, 'result1');
        }

        function testVehicleDailyEventStats() {
            const { startMonth, endMonth } = getMonthRange(1);
            callApi('/bi/onwer/selectVehicleDailyEventStats', { startMonth, endMonth }, 'result2');
        }

        function testOperationDailyEventStats() {
            const { startMonth, endMonth } = getMonthRange(1);
            callApi('/bi/onwer/selectOperationDailyEventStats', { startMonth, endMonth }, 'result3');
        }

        function testModelTotalCallTrend() {
            const { startMonth, endMonth } = getMonthRange(1);
            callApi('/bi/onwer/selectModelTotalCallTrend', { startMonth, endMonth }, 'result4');
        }

        function testDailyStats() {
            callApi('/bi/onwer/selectDailyStats', {}, 'result5');
        }

        function testArGlassRealTimeScreenStats() {
            const { startMonth, endMonth } = getMonthRange(1);
            callApi('/bi/onwer/selectArGlassRealTimeScreenStats', { startMonth, endMonth }, 'result6');
        }

        // 批量测试所有接口
        async function testAllApis() {
            console.log('开始批量测试所有接口...');
            
            testEventExceptionStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testVehicleDailyEventStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testOperationDailyEventStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testModelTotalCallTrend();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testDailyStats();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testArGlassRealTimeScreenStats();
            
            console.log('所有接口测试完成');
        }
    </script>
</body>
</html>
