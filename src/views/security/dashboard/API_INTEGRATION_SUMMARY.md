# BI大屏展示接口联调总结

## 概述

本次接口联调工作已完成，将 `src/views/security/dashboard/index.vue` 页面及其子组件的Mock数据替换为真实API调用，确保页面数据与接口返回保持一致。

## 完成的工作

### 1. 创建API接口文件

- **文件**: `src/api/bi/model.ts`
  - 定义了所有BI接口的TypeScript类型定义
  - 包含6个接口的请求参数和响应数据类型

- **文件**: `src/api/bi/dashboard.ts`
  - 实现了6个BI接口的调用方法
  - 提供了时间范围计算的工具函数
  - 使用 `securityHttp` 实例进行API调用

### 2. 修改组件实现

#### LeftSidePanel.vue
- **对接接口**:
  - `GET /bi/onwer/selectEventExceptionStats` - 业务异常情况统计
  - `GET /bi/onwer/selectVehicleDailyEventStats` - 工程车辆检修情况统计
- **主要改动**:
  - 添加API数据状态管理
  - 实现数据获取函数 `fetchBusinessExceptionData()` 和 `fetchVehicleInspectionData()`
  - 修改图表初始化函数使用真实API数据
  - 添加时间段变化监听器，自动重新获取数据

#### CenterPanel.vue
- **对接接口**:
  - `GET /bi/onwer/selectDailyStats` - 今日指标统计
- **主要改动**:
  - 添加 `fetchDailyStats()` 函数获取今日指标数据
  - 实现 `updateMetricsData()` 函数更新指标卡片显示
  - 在组件挂载时自动获取数据

#### RightSidePanel.vue
- **对接接口**:
  - `GET /bi/onwer/selectModelTotalCallTrend` - 模型总调用趋势
  - `GET /bi/onwer/selectOperationDailyEventStats` - 作业管控情况统计
- **主要改动**:
  - 添加 `fetchModelCallData()` 和 `fetchWorkControlData()` 函数
  - 修改图表初始化函数使用API数据
  - 扩展模型调用趋势图表显示成功和失败次数

#### ARGlassesPanel.vue
- **对接接口**:
  - `GET /bi/onwer/selectArGlassRealTimeScreenStats` - AR眼镜实时画面统计
- **主要改动**:
  - 添加 `fetchArGlassData()` 函数获取AR眼镜数据
  - 实现数据格式转换，将API数据转换为组件需要的格式
  - 添加刷新功能，支持手动更新数据

### 3. 接口参数处理

- **时间参数**: 实现了根据天数和月数计算 `startMonth` 和 `endMonth` 参数的工具函数
- **参数格式**: 确保时间参数格式为 `YYYY-MM` (如: `2024-01`)
- **可选参数**: 支持 `siteName` 站点名称参数（可选）

### 4. 错误处理和用户体验

- **加载状态**: 为每个数据获取操作添加了loading状态
- **错误处理**: 实现了统一的错误处理机制，API调用失败时显示错误消息
- **数据监听**: 添加了数据变化监听器，确保图表在数据更新时自动重新渲染

## 接口映射关系

| 组件 | 接口路径 | 功能描述 | 参数 |
|------|----------|----------|------|
| LeftSidePanel | `/bi/onwer/selectEventExceptionStats` | 业务异常情况统计 | startMonth, endMonth, siteName |
| LeftSidePanel | `/bi/onwer/selectVehicleDailyEventStats` | 工程车辆检修情况统计 | startMonth, endMonth, siteName |
| CenterPanel | `/bi/onwer/selectDailyStats` | 今日指标统计 | 无参数 |
| RightSidePanel | `/bi/onwer/selectModelTotalCallTrend` | 模型总调用趋势 | startMonth, endMonth, siteName |
| RightSidePanel | `/bi/onwer/selectOperationDailyEventStats` | 作业管控情况统计 | startMonth, endMonth, siteName |
| ARGlassesPanel | `/bi/onwer/selectArGlassRealTimeScreenStats` | AR眼镜实时画面统计 | startMonth, endMonth, siteName |

## 数据流程

1. **组件挂载**: 各组件在 `onMounted` 生命周期中自动获取初始数据
2. **参数变化**: 用户选择不同时间段时，自动重新获取对应数据
3. **数据更新**: API数据更新后，通过Vue的响应式系统自动更新图表
4. **错误处理**: API调用失败时，显示错误消息并保持界面稳定

## 测试验证

创建了测试页面 `test-api.html` 用于验证接口调用：
- 支持单独测试每个接口
- 支持批量测试所有接口
- 显示详细的请求和响应信息
- 提供错误诊断功能

## 注意事项

1. **认证**: 接口调用使用 `securityHttp` 实例，会自动添加认证头
2. **跨域**: 确保后端API支持跨域请求
3. **数据格式**: API返回的数据格式需要与接口文档保持一致
4. **错误码**: 建议后端返回标准的HTTP状态码和错误信息

## 后续建议

1. **数据缓存**: 考虑添加数据缓存机制，减少重复API调用
2. **实时更新**: 可以考虑使用WebSocket或定时器实现数据自动刷新
3. **性能优化**: 对于大量数据的图表，可以考虑数据分页或虚拟滚动
4. **监控告警**: 添加API调用失败的监控和告警机制

## 完成状态

✅ 创建BI API接口文件  
✅ 修改LeftSidePanel组件  
✅ 修改CenterPanel组件  
✅ 修改RightSidePanel组件  
✅ 修改ARGlassesPanel组件  
✅ 测试和验证

所有接口联调工作已完成，页面现在使用真实API数据进行展示。
