/**
 * BI大屏展示接口相关类型定义
 */

// 通用查询参数
export interface BiQueryParams {
  startMonth?: string; // 开始月份（格式如：2024-01）
  endMonth?: string;   // 结束月份（格式如：2024-07）
  siteName?: string;   // 站点名称
}

// 01 业务异常情况统计
export interface BiEventExceptionStatsVo {
  daily: string;                    // X轴时间
  totalVehicleException: number;    // 智慧检修异常数
  totalOperationException: number;  // 智慧作业异常数
  totalMaterialxception: number;    // 智慧料库异常数
}

// 02 工程车辆检修情况统计
export interface BiVehicleDailyEventStatsVo {
  total: number;      // 检修次数
  totalExcep: number; // 异常次数
  daily: string;      // X轴 每日
}

// 03 作业管控情况统计
export interface BiOperationDailyEventStatsVo {
  total: number;      // 作业次数
  totalExcep: number; // 异常次数
  daily: string;      // X轴 每日
}

// 04 模型总调用趋势
export interface BiModelTotalCallTrendVo {
  totalSuccess: number; // 成功次数
  totalFailure: number; // 失败次数
  daily: string;        // X轴 每日
}

// 05 今日指标统计
export interface BiCountDailyVo {
  activeCountDaily: number;                // 今日活跃次数
  activeCountDailyDenominator: number;     // 今日活跃次数分母
  activeCountDailyPercent: string;         // 今日活跃次数占比
  operationCountDaily: number;             // 今日作业任务
  operationCountDailyDenominator: number;  // 今日作业任务分母
  operationCountDailyPercent: string;      // 今日作业任务占比
  glassesCountDaily: number;               // 今日AR眼镜使用次数
  glassesCountDailyDenominator: number;    // 今日AR眼镜使用次数分母
  glassesCountDailyPercent: string;        // 今日AR眼镜使用占比
  vehicleCountDaily: number;               // 今日检修任务
  vehicleCountDailyDenominator: number;    // 今日检修任务分母
  vehicleCountDailyPercent: string;        // 今日检修任务占比
}

// 06 AR眼镜实时画面统计
export interface BiArGlassRealTimeScreenStatsVo {
  arGlassesName: string;  // AR眼镜名称
  pictureUrl: string;     // 封面地址
  mediaUrl: string;       // 直播地址
}

// 通用响应结构
export interface BiResponse<T> {
  code: number;
  msg: string;
  token: string;
  uri: string;
  data: T;
  request_time: string;
  response_time: string;
  cost_time: string;
  debug_image_url: string;
}
